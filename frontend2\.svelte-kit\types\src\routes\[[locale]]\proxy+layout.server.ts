// @ts-nocheck
import type {
  LayoutServerLoad,
  LayoutServerLoadEvent,
} from "./$types";

import Negotiator from "negotiator";
import { match } from "@formatjs/intl-localematcher";
import { Locale } from "$lib";

export const load = (event: Parameters<LayoutServerLoad>[0]) => {
  const preferredLocale = getPreferredLocale(event);

  return {
    preferredLocale,
  };
};

function getPreferredLocale(event: LayoutServerLoadEvent) {
  const acceptLanguage = event.request.headers.get("accept-language");

  if (acceptLanguage) {
    const negotiatorRequest = {
      headers: {
        "accept-language": acceptLanguage,
      },
    };

    const preferredLanguages = new Negotiator(negotiatorRequest).languages();

    return match(preferredLanguages, Locale._def.values, "en") as Locale;
  }

  return null;
}
